// --- Translations ---
const FIELD_TRANSLATIONS = {
  name: 'Na<PERSON>',
  email: 'E-mail',
  extra_email: 'Extra e-mail',
  type: 'Type',
  rank: 'Rang',
  rank_number: 'Rangnummer',
  private: 'Privé',
  industries: 'Branches',
  non_working_days_even_weeks: 'Rooster<PERSON><PERSON><PERSON> dagen (even weken)',
  non_working_days_uneven_weeks: '<PERSON><PERSON><PERSON><PERSON><PERSON> dagen (oneven weken)',
  team_lead: '<PERSON>persoon bouwplaats',
  active: 'Actief',
  monthly_percentage: 'Maandelijkse percentage',
  may_login: 'Mag inloggen',
  usergroup: 'Gebruikersgroep',
  customer_name: '<PERSON><PERSON><PERSON><PERSON>',
  address: '<PERSON><PERSON>',
  contact_name: 'Contactpersoon naam',
  contact_email: 'Contactpersoon e-mail',
  contact_number: 'Contactpersoon telefoonnummer',
  project_number: 'Projectnummer',
  project_number_exact: 'Projectnummer exact',
  status: 'Status',
  material_load: 'Materiaal laden',
  fte: 'FTE',
  remark: 'Opmerking',
  name_accredis: 'Naam in Accredis',
}

const VALUE_TRANSLATIONS = {
  boolean: {
    true: 'Ja',
    false: 'Nee',
    1: 'Ja',
    0: 'Nee',
    '1': 'Ja',
    '0': 'Nee',
  },
  days: {
    monday: 'Maandag',
    tuesday: 'Dinsdag',
    wednesday: 'Woensdag',
    thursday: 'Donderdag',
    friday: 'Vrijdag',
    saturday: 'Zaterdag',
    sunday: 'Zondag',
  },
}

const BOOLEAN_FIELDS = new Set([
  'private', 'team_lead', 'active', 'may_login'
])

const DAY_ARRAY_FIELDS = new Set([
  'non_working_days_even_weeks', 'non_working_days_uneven_weeks'
])

const NO_VALUE_PLACEHOLDER = 'Geen'

/**
 * Translates a field name using FIELD_TRANSLATIONS.
 * Falls back to original name if not found.
 */
export const translateFieldName = (fieldName) => {
  return FIELD_TRANSLATIONS[fieldName] ?? fieldName
}

/**
 * Translates a field value based on field type and context.
 */
export const translateFieldValue = (fieldName, value) => {
  // Handle nullish or empty values early
  if (value == null || value === '') {
    return NO_VALUE_PLACEHOLDER
  }

  // Boolean fields
  if (BOOLEAN_FIELDS.has(fieldName)) {
    return VALUE_TRANSLATIONS.boolean[value] ?? String(value)
  }

  // Day arrays
  if (DAY_ARRAY_FIELDS.has(fieldName)) {
    if (Array.isArray(value)) {
      return value.length === 0
        ? NO_VALUE_PLACEHOLDER
        : value.map(day => VALUE_TRANSLATIONS.days[day] ?? day).join(', ')
    }
    return String(value)
  }

  // Generic arrays
  if (Array.isArray(value)) {
    return value.length === 0
      ? NO_VALUE_PLACEHOLDER
      : value.join(', ')
  }

  // Objects (e.g., industries)
  if (typeof value === 'object' && !Array.isArray(value)) {
    const entries = Object.entries(value)
    return entries.length === 0
      ? NO_VALUE_PLACEHOLDER
      : entries.map(([key, val]) => `${key}: ${val ?? NO_VALUE_PLACEHOLDER}`).join(', ')
  }

  // Fallback: return as string
  return String(value)
}

/**
 * Compares old and new values in a diff object and returns human-readable changes.
 */
export const getLogDiffChanges = (diff) => {
  if (!diff || (Array.isArray(diff) && diff.length === 0)) {
    return []
  }

  const { old: oldValues = {}, new: newValues = {} } = diff
  const allFields = new Set([...Object.keys(oldValues), ...Object.keys(newValues)])
  const changes = []

  // Check if this is a new entity (all old values are null/empty)
  const isNewEntity = Object.keys(oldValues).length === 0 ||
    Object.values(oldValues).every(val => val == null || val === '')

  for (const fieldName of allFields) {
    // Skip fields that are not in FIELD_TRANSLATIONS
    if (!FIELD_TRANSLATIONS.hasOwnProperty(fieldName)) {
      continue
    }

    const oldValue = oldValues[fieldName]
    const newValue = newValues[fieldName]

    if (JSON.stringify(oldValue) === JSON.stringify(newValue)) continue

    // For new entities, only show fields that have actual values
    if (isNewEntity && (newValue == null || newValue === '')) {
      continue
    }

    changes.push({
      field: translateFieldName(fieldName),
      old: translateFieldValue(fieldName, oldValue),
      new: translateFieldValue(fieldName, newValue),
      type: isNewEntity ? 'create' : 'change'
    })
  }

  return changes
}