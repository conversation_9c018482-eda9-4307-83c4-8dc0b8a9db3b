<script setup>
import { getLogDiffChanges } from '@/helpers/translateLogs.js'

const props = defineProps({
  logs: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['refresh'])

const formatLogDate = (dateString) => {
  return new Date(dateString).toLocaleString('nl-NL', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<template>
  <v-card>
    <v-card-title class="d-flex align-center">
      <v-icon class="mr-2">
        mdi-history
      </v-icon>
      Wijzigingslogboek
      <v-spacer />
      <v-btn
        icon
        size="small"
        color="primary"
        title="Logs vernieuwen"
        :loading="loading"
        @click="emit('refresh')"
      >
        <v-icon>mdi-refresh</v-icon>
      </v-btn>
    </v-card-title>
    <v-card-text>
      <v-progress-linear
        v-if="loading"
        indeterminate
        color="primary"
        class="mb-4"
      />
      
      <div
        v-if="!loading && props.logs.length === 0"
        class="text-center text-grey py-8"
      >
        <v-icon
          size="48"
          color="grey-lighten-1"
        >
          mdi-history
        </v-icon>
        <p class="mt-2">
          Geen wijzigingen gevonden
        </p>
      </div>
      
      <v-timeline
        v-if="!loading && props.logs.length > 0"
        density="compact"
        side="end"
      >
        <v-timeline-item
          v-for="log in props.logs"
          :key="log.id"
          dot-color="primary"
          size="small"
        >
          <template #icon>
            <v-icon size="small">
              {{ log.action === 'update_entity' ? 'mdi-pencil' : 'mdi-plus' }}
            </v-icon>
          </template>
          
          <v-card>
            <v-card-text class="pb-2">
              <div class="d-flex align-center mb-2">
                <v-chip
                  :color="log.action === 'update_entity' ? 'orange' : 'green'"
                  size="small"
                  variant="flat"
                >
                  {{ log.action === 'update_entity' ? 'Bijgewerkt' : 'Aangemaakt' }}
                </v-chip>
                <v-spacer />
                <span class="text-caption text-grey">
                  {{ formatLogDate(log.changed_at) }}
                </span>
              </div>
              
              <div class="text-body-2">
                <div
                  v-for="(change, index) in getLogDiffChanges(log.diff)"
                  :key="index"
                  class="change-item"
                >
                  <div class="change-field">
                    <v-icon
                      size="small"
                      class="mr-1"
                      :color="change.type === 'create' ? 'green' : 'primary'"
                    >
                      {{ change.type === 'create' ? 'mdi-plus-circle-outline' : 'mdi-pencil-outline' }}
                    </v-icon>
                    <strong>{{ change.field }}</strong>
                  </div>
                  <div
                    v-if="change.type === 'create'"
                    class="change-values-create"
                  >
                    <v-chip
                      size="small"
                      color="green-lighten-4"
                      text-color="green-darken-2"
                      variant="flat"
                    >
                      <v-icon
                        size="small"
                        class="mr-1"
                      >
                        mdi-plus
                      </v-icon>
                      {{ change.new }}
                    </v-chip>
                  </div>

                  <div
                    v-else
                    class="change-values"
                  >
                    <span class="old-value">
                      <v-chip
                        size="small"
                        color="red-lighten-4"
                        text-color="red-darken-2"
                        variant="flat"
                        class="mr-2"
                      >
                        <v-icon
                          size="small"
                          class="mr-1"
                        >mdi-minus</v-icon>
                        {{ change.old }}
                      </v-chip>
                    </span>
                    <v-icon
                      size="small"
                      color="grey"
                      class="mx-1"
                    >
                      mdi-arrow-right
                    </v-icon>
                    <span class="new-value">
                      <v-chip
                        size="small"
                        color="green-lighten-4"
                        text-color="green-darken-2"
                        variant="flat"
                      >
                        <v-icon
                          size="small"
                          class="mr-1"
                        >mdi-plus</v-icon>
                        {{ change.new }}
                      </v-chip>
                    </span>
                  </div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-timeline-item>
      </v-timeline>
    </v-card-text>
  </v-card>
</template>

<style scoped>
.change-item {
  padding: 8px;
  border-radius: 6px;
}

.change-item:last-child {
  margin-bottom: 0;
}

.change-field {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.875rem;
}

.change-values {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.change-values-create {
  display: flex;
  align-items: center;
}

.old-value,
.new-value {
  display: inline-flex;
  align-items: center;
}

@media (max-width: 600px) {
  .change-values {
    flex-direction: column;
    align-items: flex-start;
  }

  .change-values .mx-1 {
    transform: rotate(90deg);
    margin: 4px 0;
  }
}
</style>
