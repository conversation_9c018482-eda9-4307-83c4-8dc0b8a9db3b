<script setup>
const props = defineProps({
  logs: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['refresh'])
</script>

<template>
  <v-card>
    <v-card-title class="d-flex align-center">
      <v-icon class="mr-2">
        mdi-history
      </v-icon>
      Wijzigingslogboek
      <v-spacer />
      <v-btn
        icon
        size="small"
        color="primary"
        title="Logs vernieuwen"
        :loading="loading"
        @click="emit('refresh')"
      >
        <v-icon>mdi-refresh</v-icon>
      </v-btn>
    </v-card-title>
    <v-card-text>
      <v-progress-linear
        v-if="loading"
        indeterminate
        color="primary"
        class="mb-4"
      />
      
      <div
        v-if="!loading && props.logs.length === 0"
        class="text-center text-grey py-8"
      >
        <v-icon
          size="48"
          color="grey-lighten-1"
        >
          mdi-history
        </v-icon>
        <p class="mt-2">
          Gee<PERSON> wij<PERSON> gevonden
        </p>
      </div>
      
      <v-timeline
        v-if="!loading && props.logs.length > 0"
        density="compact"
        side="end"
      >
        <v-timeline-item
          v-for="log in props.logs"
          :key="log.id"
          dot-color="primary"
          size="small"
        >
          <template #icon>
            <v-icon size="small">
              {{ log.action === 'update_entity' ? 'mdi-pencil' : 'mdi-plus' }}
            </v-icon>
          </template>
          
          <v-card>
            <v-card-text class="pb-2">
              <div class="d-flex align-center mb-2">
                <v-chip
                  :color="log.action === 'update_entity' ? 'orange' : 'green'"
                  size="small"
                  variant="flat"
                >
                  {{ log.action === 'update_entity' ? 'Bijgewerkt' : 'Aangemaakt' }}
                </v-chip>
                <v-spacer />
                <span class="text-caption text-grey">
                  {{ log.changed_at }}
                </span>
              </div>
              
              <div class="text-body-2">
                <pre class="log-diff">{{ JSON.stringify(log.diff, null, 2) }}</pre>
              </div>
            </v-card-text>
          </v-card>
        </v-timeline-item>
      </v-timeline>
    </v-card-text>
  </v-card>
</template>

<style scoped>
.log-diff {
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  background-color: rgb(var(--v-theme-surface-variant));
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}
</style>
